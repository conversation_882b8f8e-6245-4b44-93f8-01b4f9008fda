<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Slider extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = ['title', 'link', 'is_active', 'start_date', 'end_date'];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected static $filters = [
        'title' => ['type' => 'text'],
        'is_active' => ['type' => 'select', 'options' => [
            ['id' => '1', 'name' => 'Active'],
            ['id' => '0', 'name' => 'Inactive']
        ]],
        'created_at' => ['type' => 'daterange'],
        'start_date' => ['type' => 'daterange'],
    ];

    protected static $sorts = [
        'title',
        'created_at',
        'start_date',
        'end_date',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
        $this->addMediaCollection('mobile_image')->singleFile();
    }
    

    public function scopeMain($query)
    {
        return $query;//->where('is_active', true);//->orderBy('sort', 'asc');
    }

    public static function getFilters()
    {
        return static::$filters;
    }

    public static function getSorts()
    {
        return static::$sorts;
    }
}